import axios from 'axios';

/**
 * Base API configuration
 * Uses environment variables for base URL configuration
 */
const baseURL = process.env.NEXT_PUBLIC_BASE_URL || '';

const apiClient = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  config => {
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  error => Promise.reject(error)
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  response => response,
  error => {
    // Handle global error cases like 401 unauthorized
    if (error.response && error.response.status === 401) {
      // Clear auth data if needed
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
        localStorage.removeItem('selectedMatterMyMatterId');
        localStorage.removeItem('SelectedEventMatterId');

        // Redirect to login if not already there
        if (window.location.pathname !== '/signin') {
          window.location.href = '/signin';
        }
      }
    }

    return Promise.reject(error);
  }
);

export { baseURL };
export default apiClient;
