import React from 'react';
import Head from 'next/head';
import Layout from '@/components/layout/Layout';
import WorkflowSidebar from '@/components/workflows/sidebar/WorkflowSidebar';
import WorkflowsAssigned from '@/components/workflows/WorkflowsAssigned';
import { Poppins } from 'next/font/google';
import { useRouter } from 'next/router';

const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

const Workflows = () => {
  const router = useRouter();

  const handleNameClick = () => {
    router.push(
      `workflowrun?taskId=680019e55da8779ce1424284&work_flow_id=68076174e1af8cdccbe00bfc`
    );
  };

  return (
    <>
      <Head>
        <title>Workflows - FirmProfit</title>
        <meta name="description" content="Manage and view workflows in FirmProfit" />
      </Head>
      <Layout>
        <div className={`flex ml-0 h-full overflow-hidden ${poppins.className}`}>
          <WorkflowSidebar className="flex-shrink-0" />
          <div className="flex-1 overflow-auto">
            <WorkflowsAssigned onNameClick={handleNameClick} />
          </div>
        </div>
      </Layout>
    </>
  );
};

export default Workflows;
