import React from 'react';

type StatusType = 'On Track' | 'Delayed' | 'Completed' | 'Pending' | 'ON_TRACK' | 'Active' | 'Closed' | 'Open' | string;

interface StatusBadgeProps {
  status: StatusType;
  variant?: 'filled' | 'outline' | 'fill';
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  variant = 'filled',
  className = '',
}) => {
  const getStatusClasses = () => {
    const statusMapping: Record<string, string> = {
      'On Track':
        variant === 'filled' ? 'bg-[#8CF1BD] text-[#2a2e34]' : 'bg-[#8CF1BD] text-[#2a2e34]',
      Delayed: variant === 'filled' ? 'bg-[#FFDB93] text-[#2a2e34]' : 'bg-[#FFDB93] text-[#2a2e34]',
      Completed: variant === 'filled' ? 'bg-[#97C7FF] text-[#2a2e34]' : 'bg-blue-100 text-blue-800',
      Pending: variant === 'filled' ? 'bg-[#FFDB93] text-[#2a2e34]' : 'bg-[#FFDB93] text-[#2a2e34]',
      ON_TRACK:
        variant === 'filled' ? 'bg-[#8CF1BD] text-[#2a2e34]' : 'bg-[#8CF1BD] text-[#2a2e34]',
      Overdue: variant === 'filled' ? 'bg-[#EF8B8B] text-[#2A2E34]' : 'bg-[#EF8B8B] text-[#2A2E34]',
      'Due Soon':
        variant === 'filled' ? 'bg-[#FFDB93] text-[#2A2E34]' : 'bg-[#FFDB93] text-[#2A2E34]',
      Archived: variant === 'filled' ? 'bg-[#97C7FF] text-[#2a2e34]' : 'bg-[#C7D1DF] text-[#2A2E34]',
      Active: variant === 'filled' ? 'bg-[#8CF1BD] text-[#2a2e34]' : 'bg-[#8CF1BD] text-[#2a2e34]',
      Closed: variant === 'filled' ? 'bg-[#97C7FF] text-[#2a2e34]' : 'bg-[#97C7FF] text-[#2a2e34]',
      Open: variant === 'filled' ? 'bg-[#8CF1BD] text-[#2a2e34]' : 'bg-[#8CF1BD] text-[#2a2e34]',
    };

    console.log(
      '🚀 ~ getStatusClasses ~ statusMapping[status]:*************',
      statusMapping[status]
    );
    return statusMapping[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <span className={`px-[12px] py-[4px] w-[100px] rounded-full text-[12px] text-center ${getStatusClasses()} ${className}`}>
      {status}
    </span>
  );
};

export default StatusBadge;
