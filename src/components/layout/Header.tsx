import React from 'react';
// import { useSelector, useDispatch } from 'react-redux';
// import { RootState } from '@/redux/types';
import { LogOut, Plus } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/router';
import { toast } from 'react-hot-toast';
// import { logoutUser } from '@/features/user/userSlice';
// import { useRouter } from 'next/router';

const Header: React.FC = () => {
  // const user = useSelector((state: RootState) => state.user);
  // const dispatch = useDispatch();
  // const router = useRouter();

  // const handleLogout = () => {
  //   dispatch(logoutUser());
  //   localStorage.removeItem('user');
  //   router.push('/signin');
  // };
  // background: #ECF1FE;
  const router = useRouter();

  const handleLogout = () => {
    // Add your logout logic here
    router.push('/signin');
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    localStorage.removeItem('reviewTasks');
    localStorage.setItem('Mfa', JSON.stringify(false));
    localStorage.removeItem('isUpdated');
    localStorage.removeItem('isEventAdded');
    localStorage.removeItem('selectedMatters');
    localStorage.removeItem('timezone');
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('isEventAdded_')) {
        localStorage.removeItem(key);
      }
    });
    localStorage.removeItem('selectedMatterMyMatterId');
    localStorage.removeItem('SelectedEventMatterId');
    localStorage.removeItem('matterList');
    
  };

  const handleNewWorkflow = async () => {
    localStorage.removeItem('isUpdated');
    localStorage.removeItem('selectedMatterMyMatterId');
    localStorage.removeItem('SelectedEventMatterId');
    localStorage.removeItem('selectedMatters');
    localStorage.removeItem('matterList');
    localStorage.removeItem('isEventAdded');
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('isEventAdded_')) {
        localStorage.removeItem(key);
      }
    });
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL}/workflow/demo-workflow`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'template_id=680760d0e1af8cdccbe00bfa',
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Workflow created successfully:', data);
        toast.success('Workflow created successfully');
        window.location.href = `/court-notice/new`;
      } else {
        console.error('Failed to create workflow:', response.statusText);
        toast.error('Failed to create workflow');
      }
    } catch (error) {
      console.error('Error creating workflow:', error);
      toast.error('Error creating workflow');
    }
  };

  return (
    <header className="h-[60px] bg-white border-b border-gray-200 flex items-center justify-between px-4 z-10">
      <div className="flex items-center">
        <Image
          src="/logo.svg"
          alt="FirmProfit Logo"
          width={169}
          height={30}
          className="text-white"
        />
      </div>

      <div className="flex items-center space-x-3">
        <button className="p-1.5 rounded-[12px] hover:bg-gray-100">
          <Image src={`/assets/search.svg`} alt="Sidebar" width={20} height={20} />
        </button>
        <button className="p-1.5 rounded-[12px] hover:bg-gray-100">
          <Image src={`/assets/settings-02.svg`} alt="settings" width={20} height={20} />
        </button>
        <button className="p-1.5 rounded-[12px] hover:bg-gray-100">
          <Image src={`/assets/question-circle.svg`} alt="question" width={20} height={20} />
        </button>
        <button
          onClick={handleNewWorkflow}
          className="bg-[#3f73f6] cursor-pointer h-[40px] text-white px-[16px] py-[12px] gap-[4px] rounded-[12px] flex items-center text-sm font-medium"
        >
          <Plus className="h-4 w-4 mr-1" /> New
        </button>
        <button
          onClick={handleLogout}
          className="bg-[#3f73f6] cursor-pointer h-[40px] text-white px-[16px] py-[12px] gap-[4px] rounded-[12px] flex items-center text-sm font-medium"
        >
          <LogOut className="h-4 w-4 mr-1" /> Sign out
        </button>
        <div className="flex w-[57px] h-[38px] rounded-[19px] p-[4px] bg-[#ECF1FE] items-center ml-1">
          <button className="rounded-full overflow-hidden">
            <div className="flex">
              <Image
                src={'/assets/user-admin.svg'}
                alt="User Avatar"
                width={28}
                height={28}
                className="cursor-pointer"
              />
              <Image src={`/assets/chevron-down.svg`} alt="question" width={20} height={20} />
            </div>
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;
