import { apiGet } from './apiUtils';
import { WorkflowResponse } from '@/types/workflow';

/**
 * Workflow API service
 * Handles all workflow-related API operations
 */
const workflowService = {
  /**
   * Get workflows assigned to the current user
   * @param page - Page number for pagination
   * @param limit - Number of items per page
   * @returns Promise with workflow data
   */
  getMyWorkflows: async (page: number = 1, limit: number = 10): Promise<WorkflowResponse> => {
    const response = await apiGet<WorkflowResponse>(
      `/workflow/my-workflow?page=${page}&limit=${limit}`
    );
    return response.data;
  },

  /**
   * Get a specific workflow by ID
   * @param id - Workflow ID
   * @returns Promise with workflow data
   */
  getWorkflowById: async (id: string) => {
    const response = await apiGet(`/workflow/${id}`);
    return response.data;
  },
};

export default workflowService;
