import Head from 'next/head';
import { useEffect } from 'react';
import { useRouter } from 'next/router';

/**
 * Landing page that handles authentication redirects
 */
export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Check if the user is authenticated
    const token = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;

    if (token) {
      // If authenticated, redirect to dashboard or home
      router.push('/court-notice/new');
    } else {
      // If not authenticated, redirect to signin
      router.push('/signin');
    }
  }, [router]);

  return (
    <>
      <Head>
        <title>FirmProfit</title>
        <meta name="description" content="FirmProfit" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* Correct way to use an SVG as a favicon */}
        <link rel="icon" href="/favicon.svg" type="image/svg+xml" />

        {/* Optional: Add a PNG fallback for browsers that don't support SVG favicons */}
        <link rel="icon" href="/favicon.png" type="image/png" />
      </Head>
      <main></main>
    </>
  );
}
