declare module 'column-resizer' {
  interface ColumnResizerOptions {
    liveDrag?: boolean;
    draggingClass?: string;
    gripInnerHtml?: string;
    minWidth?: number;
    headerOnly?: boolean;
    hoverCursor?: string;
    dragCursor?: string;
    postbackSafe?: boolean;
    flush?: boolean;
    marginLeft?: string;
    marginRight?: string;
    disable?: boolean;
    partialRefresh?: boolean;
    removePadding?: boolean;
    remoteTable?: HTMLTableElement;
  }

  class ColumnResizer {
    constructor(table: HTMLTableElement, options?: ColumnResizerOptions);
    reset(options?: ColumnResizerOptions): ColumnResizerOptions;
  }

  export default ColumnResizer;
}