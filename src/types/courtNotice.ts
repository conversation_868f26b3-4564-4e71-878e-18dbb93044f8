/**
 * Court Notice related types
 */

// Court Notice item structure
export interface CourtNoticeItem {
  id: number;
  template_name: string;
  start_date: string;
  end_date: string;
  last_activity: string;
  status: string;
  assigned_users: string;
  assign_by: string;
  run_by: string;
  notes: string;
  work_flow_runner: string;
  attorney: string;
  matter: string;
  last_task_id: string;
  _id: string;
  isChild?: string;
  completed_tasks?: string;
}

// API response structure for Court Notice list
export interface CourtNoticeListResponse {
  data: {
    fields: CourtNoticeItem[];
    total: number;
    page: number;
    limit: number;
  };
  statusCode: number;
  message: string;
}

// Query parameters for Court Notice list API
export interface CourtNoticeListParams {
  page: number;
  limit: number;
  type: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  userId?: string; // Made optional since it's automatically added by the service
}

// Status state for Court Notice module
export interface CourtNoticeStatus {
  loading: boolean;
  error: string | null;
}

// Status variant mapping
export const mapStatusVariant = (status: string): 'On Track' | 'Pending' | 'Completed' | 'Overdue' | 'Due Soon' | 'Archived' => {
  const statusMap: Record<string, 'On Track' | 'Pending' | 'Completed' | 'Overdue' | 'Due Soon' | 'Archived'> = {
    PENDING: 'Pending',
    COMPLETED: 'Completed',
    IN_PROGRESS: 'On Track',
    ON_TRACK: 'On Track',
    OVERDUE: 'Overdue',
    DUE_SOON: 'Due Soon',
    ARCHIVED: 'Archived'
  };

  return statusMap[status] || 'Pending';
};

/**
 * Check if a string value contains "TBD" (case insensitive)
 * @param value The string to check
 * @returns True if the string contains "TBD", false otherwise
 */
export const isTbdValue = (value: string | null | undefined): boolean => {
  return typeof value === 'string' && value.toLowerCase().includes('tbd');
};

/**
 * Fields that should be checked for TBD values in court notice events
 */
export const tbdCheckFields = [
  'courtNoticeType',
  'appointmentAction',
  'charge',
  'county',
  'courtLocation',
  'clientAttendance',
  'meetingLocation',
];

/**
 * Count the number of TBD fields in a court notice event
 * @param event The court notice event to check
 * @returns The number of fields with TBD values
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const countTbdFields = (event: Record<string, any>): number => {
  let count = 0;

  // Check standard fields
  tbdCheckFields.forEach(field => {
    if (isTbdValue(event[field])) {
      count++;
    }
  });

  // Also check meeting details based on the meeting location type
  if (event.meetingLocation) {
    if (event.meetingLocation.includes('Virtual meeting link') && isTbdValue(event.meetingLink)) {
      count++;
    }

    if (event.meetingLocation.includes('Phone') && isTbdValue(event.phoneDetails)) {
      count++;
    }

    if (event.meetingLocation.includes('Meeting in person') && isTbdValue(event.meetingAddress)) {
      count++;
    }
  }

  return count;
};
