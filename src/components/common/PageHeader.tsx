import React, { ReactNode } from 'react';
import TableActions from './TableActions';

interface PageHeaderProps {
  title: string;
  searchValue: string;
  onSearchChange: (value: string) => void;
  onFilter?: () => void;
  onColumns?: () => void;
  onSaveView?: () => void;
  actions?: ReactNode;
  showFilter?: boolean;
  showColumns?: boolean;
  showSaveView?: boolean;
  className?: string;
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  searchValue,
  onSearchChange,
  onFilter,
  onColumns,
  onSaveView,
  actions,
  showFilter = true,
  showColumns = true,
  showSaveView = true,
  className = '',
}) => {
  return (
    <div className={`flex justify-between items-center mb-6 ${className}`}>
      <h1 className="text-2xl font-medium text-[#2A2E34]">{title}</h1>
      <div className="flex items-center gap-2">
        <TableActions
          searchValue={searchValue}
          onSearchChange={onSearchChange}
          onFilter={onFilter}
          onColumns={onColumns}
          onSaveView={onSaveView}
          showFilter={showFilter}
          showColumns={showColumns}
          showSaveView={showSaveView}
        />
        {actions}
      </div>
    </div>
  );
};

export default PageHeader;
