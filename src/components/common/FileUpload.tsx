/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * FileUpload Component - Reset and Fixed Version
 * 
 * This component handles file uploads to S3 with proper renaming and access functionality.
 * 
 * Key Features:
 * 1. Proper S3 key tracking for upload and rename operations
 * 2. Clean separation between display names and S3 filenames
 * 3. Reliable file access via presigned URLs
 * 4. Robust rename functionality
 * 5. Multiple file format support
 */
import React, { useState, useCallback, useRef, forwardRef, useImperativeHandle, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { X, FileText, Image as ImageIcon } from 'lucide-react';
import { VALIDATIONS } from '@/constants/workflow';

export interface UploadedFile {
  id: string;
  name?: string; // Display name shown to user
  s3Key?: string; // Current S3 key (path + filename)
  originalS3Key?: string; // Original S3 key when first uploaded
  s3FileName?: string; // S3 filename with unique ID
  displayName?: string; // Display name for UI
  size?: number;
  type?: string;
  url?: string;
  progress?: number;
  status: 'uploading' | 'completed' | 'error';
  error?: string;
  originalName?: string; // Original file name from user's computer
  isDeleted?: boolean;
  isNameChanged?: boolean;
  isEditing?: boolean;
  path?: string;
  editingName?: string;
  uniqueId?: string;
  key?: string; // For backward compatibility
  oldS3Key?: string; // For rename operations
  oldFileName?: string; // For rename operations
}

interface FileUploadProps {
  value: UploadedFile[];
  onChange: React.Dispatch<React.SetStateAction<UploadedFile[]>>;
  maxFileSize?: number;
  allowedFileTypes?: string[];
  maxFiles?: number;
  path?: string;
  courtNoticeType?: string;
  clientLastName?: string;
  actionType?: string;
  startDate?: string;
  disabled?: boolean;
}

export interface FileUploadRef {
  getFilesForEvent: () => Array<{
    uniqueId: string | undefined;
    name: string | undefined;
    s3FileName: string | undefined;
    url: string | undefined;
    key: string | undefined;
    originalName: string | undefined;
    size: number | undefined;
    type: string | undefined;
  }>;
}

const FileUpload = forwardRef<FileUploadRef, FileUploadProps>(({
  value,
  onChange,
  maxFileSize = 5 * 1024 * 1024, // 5MB default as requested
  allowedFileTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/svg+xml',
    'image/tiff',
    'image/bmp',
    'image/gif'
  ],
  maxFiles = 50,
  path = '',
  courtNoticeType = '',
  clientLastName = '',
  actionType,
  startDate,
  disabled = false,
}, ref) => {
  const [isDragging, setIsDragging] = useState(false);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [isGeneratingUrl, setIsGeneratingUrl] = useState<Record<string, boolean>>({});
  const [inputWidths, setInputWidths] = useState<Record<string, number>>({});
  const [showFileLimitAlert, setShowFileLimitAlert] = useState(false);
  const measureRef = useRef<HTMLSpanElement>(null);


  const mimeTypeToExtensions: Record<string, string[]> = {
    'application/pdf': ['PDF'],
    'application/msword': ['DOC'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['DOCX'],
    'application/vnd.ms-excel': ['XLS'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['XLSX'],
    'image/jpeg': ['JPEG'],
    'image/jpg': ['JPG'],
    'image/png': ['PNG'],
    'image/svg+xml': ['SVG'],
    'image/tiff': ['TIFF'],
    'image/bmp': ['BMP'],
    'image/gif': ['GIF']
  };

  const supportedFormats = Array.from(new Set(
    allowedFileTypes.flatMap(type => mimeTypeToExtensions[type] || [])
  )).join(', ');

  // Expose the getFilesForEvent function to parent components
  useImperativeHandle(ref, () => ({
    getFilesForEvent: () => {
      return value
        .filter(file => file.status === 'completed' && !file.isDeleted)
        .map(file => ({
          uniqueId: file.uniqueId,
          name: file.name,
          s3FileName: file.s3FileName,
          url: file.url,
          key: file.s3Key,
          originalName: file.originalName,
          size: file.size,
          type: file.type
        }));
    }
  }));

  // Function to measure text width for input fields
  const measureTextWidth = (text: string): number => {
    if (!measureRef.current) return 200; // fallback width

    measureRef.current.textContent = text;
    const width = measureRef.current.offsetWidth;

    // Add some padding and ensure minimum width
    const minWidth = 150;
    const maxWidth = 400; // Maximum width to prevent overflow
    const calculatedWidth = Math.max(minWidth, Math.min(maxWidth, width + 20));

    return calculatedWidth;
  };

  // Update input width when file name changes
  const updateInputWidth = (fileId: string, text: string) => {
    const width = measureTextWidth(text);
    setInputWidths(prev => ({ ...prev, [fileId]: width }));
  };

  useEffect(() => {
    // Check if we now have all required fields
    const hasRequiredFields = () => {
      if (!courtNoticeType || courtNoticeType.trim() === '') return false;

      return true;
    };

    if (hasRequiredFields()) {
      // Remove error files that were caused by validation issues
      onChange(prev => prev.filter(file =>
        !(file.status === 'error' && file.error === VALIDATIONS.FILE_UPLOAD_VALIDATION)
      ));
    }
  }, [courtNoticeType, actionType, startDate, onChange]);


  // Generate unique ID for files
  const generateUniqueId = (): string => {
    return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  };

  // Generate S3 filename with proper format
  const generateS3FileName = (originalName: string, customName?: string, existingUniqueId?: string): { s3FileName: string; displayName: string; uniqueId: string } => {
    const extension = originalName.split('.').pop();
    const parts = clientLastName.split(' ');
    const clientName = parts[1] ? parts[1] : clientLastName;

    const now = new Date();
    const month = String(now.getUTCMonth() + 1).padStart(2, '0');
    const day = String(now.getUTCDate()).padStart(2, '0');
    const year = now.getUTCFullYear();
    const currentDateTime = `${month}-${day}-${year}`;
    const uniqueId = existingUniqueId || generateUniqueId();

    let s3FileName: string;
    let displayName: string;

    if (customName && existingUniqueId) {
      s3FileName = `${customName} ${uniqueId}.${extension}`;
      displayName = `${customName}.${extension}`;
    } else {
      const baseName = customName || courtNoticeType;
      s3FileName = `${baseName} ${currentDateTime} ${clientName} ${uniqueId}.${extension}`;
      displayName = `${baseName} ${currentDateTime} ${clientName}.${extension}`;
    }

    return { s3FileName, displayName, uniqueId };
  };

  const getFileTypeFromMultipleSources = (file: UploadedFile): string | undefined => {
    const getExtension = (filename: string): string | undefined => {
      return filename.split('.').pop()?.toLowerCase();
    };
    const getTypeFromExtension = (extension: string | undefined): string | undefined => {
      if (!extension) return undefined;

      switch (extension) {
        case 'pdf':
          return 'application/pdf';
        case 'jpg':
        case 'jpeg':
          return 'image/jpeg';
        case 'png':
          return 'image/png';
        case 'gif':
          return 'image/gif';
        case 'bmp':
          return 'image/bmp';
        case 'svg':
          return 'image/svg+xml';
        case 'tiff':
        case 'tif':
          return 'image/tiff';
        case 'doc':
        case 'docx':
          return 'application/msword';
        case 'xls':
        case 'xlsx':
          return 'application/vnd.ms-excel';
        default:
          return undefined;
      }
    };
    if (file.s3FileName) {
      const extension = getExtension(file.s3FileName);
      const determinedType = getTypeFromExtension(extension);
      if (determinedType) {
        return determinedType;
      }
    }

    if (file.originalName) {
      const extension = getExtension(file.originalName);
      const determinedType = getTypeFromExtension(extension);
      if (determinedType) {
        return determinedType;
      }
    }

    if (file.name) {
      const extension = getExtension(file.name);
      const determinedType = getTypeFromExtension(extension);
      if (determinedType) {
        return determinedType;
      }
    }

    if (file.type) {
      const hasExtension = file.originalName || file.name || file.s3FileName;

      if (!hasExtension) {
        return file.type;
      } else {
        return undefined;
      }
    }

    return undefined;
  };

  // Validate file before upload
  const validateFile = (file: File): string | null => {
    if (!courtNoticeType || courtNoticeType.trim() === '') {
      return VALIDATIONS.FILE_UPLOAD_VALIDATION;
    }

    if (!allowedFileTypes.includes(file.type)) {
      return 'File type not allowed';
    }
    if (file.size > maxFileSize) {
      return 'File size is too large';
    }

    // Count only non-deleted files for the limit check
    const activeFileCount = value.filter(f => !f.isDeleted).length;
    if (activeFileCount >= maxFiles) {
      return `Maximum ${maxFiles} files allowed`;
    }
    return null;
  };

  // Improved validation function that waits for state updates
  const handleFileValidation = (file: File): Promise<string | null> => {
    return new Promise((resolve) => {
      // First, try immediate validation
      const immediateValidation = validateFile(file);

      if (immediateValidation === null) {
        // If validation passes immediately, resolve
        resolve(null);
        return;
      }

      // If validation fails due to missing required fields, wait longer for state updates
      if (immediateValidation === VALIDATIONS.FILE_UPLOAD_VALIDATION) {
        // Wait longer for React state updates to propagate
        setTimeout(() => {
          const delayedValidation = validateFile(file);
          resolve(delayedValidation);
        }, 300); // Increased delay to ensure state propagation
      } else {
        // For other validation errors (file type, size, etc.), resolve immediately
        resolve(immediateValidation);
      }
    });
  };

  // Upload file to S3
  const uploadToS3 = async (
    file: File,
    presignedUrl: string,
    onProgress: (progress: number) => void,
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('PUT', presignedUrl, true);
      xhr.setRequestHeader('Content-Type', file.type);

      xhr.upload.onprogress = event => {
        if (event.lengthComputable) {
          const percent = Math.round((event.loaded / event.total) * 100);
          onProgress(percent);
        }
      };

      xhr.onload = () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          resolve(presignedUrl.split('?')[0]);
        } else {
          reject(new Error(`Upload failed with status: ${xhr.status}`));
        }
      };

      xhr.onerror = () => {
        reject(new Error('Failed to upload file to S3'));
      };

      xhr.send(file);
    });
  };

  // Get presigned URL for viewing files
  const getPresignedViewUrl = async (s3Key: string) => {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/get-presigned-url?key=${encodeURIComponent(s3Key)}&operation=get`
      );
      if (!response.ok) {
        throw new Error('Failed to get view URL');
      }
      const data = await response.json();
      if (!data.data?.url) {
        throw new Error('Invalid view URL response');
      }
      return data.data.url;
    } catch (error) {
      console.error('Error getting view URL:', error);
      throw error;
    }
  };

  // Handle file upload
  const handleFileUpload = async (file: File) => {
    const validationError = await handleFileValidation(file);
    if (validationError) {
      onChange(prev => [
        ...prev,
        {
          id: `error-${Date.now()}`,
          name: file.name,
          type: file.type,
          size: file.size,
          status: 'error' as const,
          progress: 0,
          error: validationError,
          isDeleted: false,
        },
      ]);
      return;
    }

    const fileId = `${file.name}-${Date.now()}`;
    const { s3FileName, displayName, uniqueId } = generateS3FileName(file.name);
    const s3Key = path + s3FileName;

    // Add file to state with uploading status
    onChange(prev => [
      ...prev,
      {
        id: fileId,
        name: displayName,
        s3Key: s3Key,
        originalS3Key: s3Key,
        s3FileName: s3FileName,
        displayName: displayName,
        originalName: file.name,
        size: file.size,
        type: file.type,
        url: '',
        progress: 0,
        status: 'uploading',
        path: path,
        uniqueId,
        isNameChanged: false,
        key: s3Key, // For backward compatibility
      },
    ]);

    try {
      // Get presigned URL for upload
      const uploadResponse = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/workflow/get-presigned-url?key=${encodeURIComponent(s3Key)}&operation=put`
      );

      if (!uploadResponse.ok) {
        throw new Error('Failed to get presigned URL');
      }

      const uploadData = await uploadResponse.json();
      if (!uploadData.data?.url) {
        throw new Error('Invalid presigned URL response');
      }

      // Upload the file
      await uploadToS3(file, uploadData.data.url, progress => {
        onChange(prev =>
          prev.map(f =>
            f.id === fileId ? { ...f, progress } : f
          )
        );
      });

      // Get view URL
      const viewUrl = await getPresignedViewUrl(s3Key);

      // Update file with completed status and view URL
      onChange(prev =>
        prev.map(f =>
          f.id === fileId
            ? {
              ...f,
              url: viewUrl,
              progress: 100,
              status: 'completed' as const,
            }
            : f
        )
      );
    } catch (error) {
      console.error('Error uploading file:', error);
      onChange(prev =>
        prev.map(f =>
          f.id === fileId
            ? {
              ...f,
              progress: 100,
              status: 'error' as const,
              error: error instanceof Error ? error.message : 'Upload failed',
            }
            : f
        )
      );
    }
  };

  // Handle file name update during editing
  const handleFileNameUpdate = (fileId: string, newName: string) => {
    updateInputWidth(fileId, newName);

    onChange(prev =>
      prev.map(file => {
        if (file.id === fileId) {
          return {
            ...file,
            editingName: newName,
            isNameChanged: true
          };
        }
        return file;
      })
    );
  };

  // Get filename without extension for editing
  const getFileNameForEditing = (file: UploadedFile): string => {
    if (file.editingName !== undefined) {
      return file.editingName;
    }

    const nameToUse = file.name;
    if (nameToUse && nameToUse.includes('.')) {
      return nameToUse.substring(0, nameToUse.lastIndexOf('.'));
    }

    return nameToUse || '';
  };

  // Toggle edit mode for a file
  const toggleEditMode = async (fileId: string) => {
    const file = value.find(f => f.id === fileId);
    if (!file) return;

    if (file.isEditing) {
      // Save the edited name
      const editingName = file.editingName || getFileNameForEditing(file);

      if (!editingName.trim()) {
        onChange(prev =>
          prev.map(f => {
            if (f.id === fileId) {
              return {
                ...f,
                error: 'File name cannot be empty',
                status: 'error' as const
              };
            }
            return f;
          })
        );
        return;
      }

      const originalExtension = file.originalName ?
        file.originalName.split('.').pop() :
        file.name?.split('.').pop();

      const fullDisplayName = originalExtension ?
        `${editingName.trim()}.${originalExtension}` :
        editingName.trim();

      // Check if name actually changed
      const hasNameChanged = file.name !== fullDisplayName;

      if (hasNameChanged) {
        // Generate new S3 filename using existing unique ID to maintain consistency
        const { s3FileName: newS3FileName } = generateS3FileName(file.originalName || '', editingName.trim(), file.uniqueId);
        // Use the path prop instead of file.path to ensure correct prefix
        const newS3Key = (path || '') + newS3FileName;

        try {
          // Get new view URL for renamed file
          const newViewUrl = await getPresignedViewUrl(newS3Key);

          onChange(prev =>
            prev.map(f => {
              if (f.id === fileId) {
                return {
                  ...f,
                  name: fullDisplayName,
                  displayName: fullDisplayName,
                  s3Key: newS3Key,
                  s3FileName: newS3FileName,
                  url: newViewUrl,
                  editingName: undefined,
                  isEditing: false,
                  error: undefined,
                  status: 'completed' as const,
                  isNameChanged: true,
                  key: newS3Key, // For backward compatibility
                  oldS3Key: file.s3Key, // Store old S3 key for rename operations
                };
              }
              return f;
            })
          );
        } catch (error) {
          console.error('Error generating URL for renamed file:', error);
          onChange(prev =>
            prev.map(f => {
              if (f.id === fileId) {
                return {
                  ...f,
                  name: fullDisplayName,
                  displayName: fullDisplayName,
                  s3Key: newS3Key,
                  s3FileName: newS3FileName,
                  editingName: undefined,
                  isEditing: false,
                  error: undefined,
                  status: 'completed' as const,
                  isNameChanged: true,
                  key: newS3Key, // For backward compatibility
                  oldS3Key: file.s3Key, // Store old S3 key for rename operations
                };
              }
              return f;
            })
          );
        }
      } else {
        // No name change, just exit edit mode
        onChange(prev =>
          prev.map(f => {
            if (f.id === fileId) {
              return {
                ...f,
                editingName: undefined,
                isEditing: false,
                error: undefined,
                status: 'completed' as const
              };
            }
            return f;
          })
        );
      }
    } else {
      // Enter edit mode
      onChange(prev =>
        prev.map(f => {
          if (f.id === fileId) {
            const nameForEditing = getFileNameForEditing(f);
            setTimeout(() => updateInputWidth(fileId, nameForEditing), 0);

            return {
              ...f,
              isEditing: true,
              editingName: nameForEditing,
              error: undefined,
              status: 'completed' as const
            };
          }
          return f;
        })
      );
    }
  };

  // Handle file click to open/view
  const handleFileClick = async (e: React.MouseEvent<HTMLAnchorElement>, file: UploadedFile) => {
    e.preventDefault();
    if (!file.s3Key) return;

    // If we have a valid URL, use it
    if (file.url) {
      window.open(file.url, '_blank');
      return;
    }

    // Generate new presigned URL
    try {
      setIsGeneratingUrl(prev => ({ ...prev, [file.id]: true }));
      const viewUrl = await getPresignedViewUrl(file.s3Key);

      // Update file with new URL
      onChange(prev =>
        prev.map(f =>
          f.id === file.id ? { ...f, url: viewUrl } : f
        )
      );

      window.open(viewUrl, '_blank');
    } catch (error) {
      console.error('Error opening file:', error);
    } finally {
      setIsGeneratingUrl(prev => ({ ...prev, [file.id]: false }));
    }
  };

  // Handle file deletion
  const handleDelete = (fileId: string) => {
    setDeleteConfirmId(fileId);
  };

  const confirmDelete = () => {
    if (deleteConfirmId) {
      // Instead of removing the file completely, mark it as deleted
      // This ensures it appears in the deleteFiles array when form is submitted
      onChange(prev =>
        prev.map(f =>
          f.id === deleteConfirmId
            ? { ...f, isDeleted: true }
            : f
        )
      );
      setDeleteConfirmId(null);
    }
  };

  const cancelDelete = () => {
    setDeleteConfirmId(null);
  };

  const handleDirectDelete = (fileId: string) => {
    console.log('handleDirectDelete called for fileId:', fileId);
    console.log('Current files before delete:', value);

    onChange(prev => {
      const updatedFiles = prev.map(f =>
        f.id === fileId
          ? { ...f, isDeleted: true }
          : f
      );
      console.log('Updated files after delete:', updatedFiles);
      return updatedFiles;
    });
  };

  // Drag and drop handlers
  const handleDrop = useCallback(
    async (e: React.DragEvent<HTMLDivElement>) => {
      e.preventDefault();
      setIsDragging(false);

      const files = Array.from(e.dataTransfer.files);

      // Count existing non-deleted files
      const existingFileCount = value.filter(f => !f.isDeleted).length;

      // Check if total files (existing + new) would exceed the limit
      if (existingFileCount + files.length > maxFiles) {
        setShowFileLimitAlert(true);
        return;
      }

      await Promise.all(files.map(file => handleFileUpload(file)));
    },
    [courtNoticeType, actionType, startDate, value, onChange, maxFiles]
  );

  const handleFileInput = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const files = Array.from(e.target.files || []);

      // Count existing non-deleted files
      const existingFileCount = value.filter(f => !f.isDeleted).length;

      // Check if total files (existing + new) would exceed the limit
      if (existingFileCount + files.length > maxFiles) {
        setShowFileLimitAlert(true);
        e.target.value = '';
        return;
      }

      await Promise.all(files.map(file => handleFileUpload(file)));
      e.target.value = '';
    },
    [courtNoticeType, actionType, startDate, value, onChange, maxFiles]
  );

  // Helper functions
  const getFileIcon = (fileType: string | undefined, fileName?: string) => {
    if (fileType === 'application/pdf') {
      return <img alt="pdf" loading="lazy" width="22" height="22" decoding="async" data-nimg="1" src="/assets/pdf-filled.svg" />
    }
    if (fileType?.startsWith('image/')) {
      return <ImageIcon className="w-5 h-5 text-[#3F73F6]" />;
    }
    if (fileType === 'application/msword' || fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
      return <img alt="word" loading="lazy" width="22" height="22" decoding="async" data-nimg="1" src="/assets/word.svg" />
    }
    if (fileType === 'application/vnd.ms-excel' || fileType === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
      return <img alt="excel" loading="lazy" width="22" height="22" decoding="async" data-nimg="1" src="/assets/excel.svg" />;
    }

    if (fileName) {
      const extension = fileName.split('.').pop()?.toLowerCase();

      if (extension === 'pdf') {
        return <img alt="pdf" loading="lazy" width="22" height="22" decoding="async" data-nimg="1" src="/assets/pdf-filled.svg" />
      }
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'tiff', 'tif', 'webp'].includes(extension || '')) {
        return <ImageIcon className="w-5 h-5 text-[#3F73F6]" />;
      }
      if (['doc', 'docx'].includes(extension || '')) {
        return <FileText className="w-5 h-5 text-[#2563EB]" />;
      }
      if (['xls', 'xlsx'].includes(extension || '')) {
        return <FileText className="w-5 h-5 text-[#16A34A]" />;
      }
    }

    return <FileText className="w-5 h-5 text-[#5F6F84]" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getProgressBarColor = (status: string) => {
    switch (status) {
      case 'uploading': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-300';
    }
  };

  const getTextColor = (status: string) => {
    switch (status) {
      case 'uploading': return 'text-blue-500';
      case 'completed': return 'text-green-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = (file: UploadedFile) => {
    switch (file.status) {
      case 'uploading': return `${file.progress}%`;
      case 'completed': return '100%';
      case 'error': return file.error || '';
      default: return '';
    }
  };

  const truncateFileName = (fileName: string, maxLength: number = 38) => {
    if (!fileName) return '';
    if (fileName.length <= maxLength) return fileName;

    const extension = fileName.split('.').pop() || '';
    const nameWithoutExt = fileName.slice(0, fileName.lastIndexOf('.'));

    // Calculate how much of the name we can show
    const truncatedLength = maxLength - extension.length - 4; // 4 accounts for "..." and "."
    const truncatedName = nameWithoutExt.slice(0, truncatedLength);

    return `${truncatedName}...${extension ? `.${extension}` : ''}`;
  };

  const isUploadDisabled = () => {
    // Check if component is explicitly disabled
    if (disabled) {
      return true;
    }

    // Check if court notice type is missing
    if (!courtNoticeType || courtNoticeType.trim() === '') {
      return true;
    }

    // Count only non-deleted files for the limit check
    const activeFileCount = value.filter(f => !f.isDeleted).length;
    return activeFileCount >= maxFiles;
  };

  const getUploadMessage = () => {
    if (disabled) {
      return 'Upload is disabled';
    }

    if (!courtNoticeType || courtNoticeType.trim() === '') {
      return 'Please add the Court Notice Type and Court notice action/Start date before uploading a file';
    }

    // Count only non-deleted files for the limit check
    const activeFileCount = value.filter(f => !f.isDeleted).length;
    if (activeFileCount >= maxFiles) {
      return `Maximum ${maxFiles} files reached`;
    }

    return 'Upload file';
  };

  const canUploadFile = () => {
    // Check if component is explicitly disabled
    if (disabled) {
      return false;
    }

    // Check if court notice type is provided
    if (!courtNoticeType || courtNoticeType.trim() === '') {
      return false;
    }

    if (actionType === 'Calendar event and save court notice' ||
      actionType === 'Calendar DEADLINE and save court notice' ||
      actionType === 'Save court notice only') {
      return !!(courtNoticeType);
    }

    // For other action types, just check court notice type
    return !!courtNoticeType;
  };

  return (
    <div className="space-y-4">
      {/* Hidden span for measuring text width */}
      <span
        ref={measureRef}
        className="absolute invisible text-sm font-medium"
        style={{ whiteSpace: 'nowrap', fontSize: '14px', fontWeight: '500' }}
        aria-hidden="true"
      />

      {/* Delete Confirmation Modal */}
      {deleteConfirmId && ReactDOM.createPortal(
        <div className="fixed inset-0 z-[9999] flex items-center justify-center" style={{ backgroundColor: 'rgba(128, 128, 128, 0.3)' }}>
          <div className="bg-white rounded-xl shadow-xl p-8 w-full max-w-md flex flex-col items-center relative animate-fade-in">
            <div className="flex flex-col items-center">
              <div className="rounded-full p-3 mb-3">
                <img alt="Delete" loading="lazy" width="50" height="50" decoding="async" src="assets/info-circle.svg" />
              </div>
              <span className="text-base font-semibold text-gray-900 mb-2">Delete this file?</span>
              <p className="text-gray-600 text-sm text-center mb-6">Are you sure you want to delete this file? <br />This process can&apos;t be undone.</p>
            </div>
            <div className="flex gap-3 w-full justify-center">
              <button
                className="w-[162px] h-[40px] rounded-lg bg-red-200 text-white hover:bg-red-400 transition-colors"
                onClick={cancelDelete}
              >
                No
              </button>
              <button
                type="button"
                onClick={confirmDelete}
                className="w-[162px] h-[40px] border border-[#3F73F6] cursor-pointer text-[#3F73F6] rounded-[12px] bg-white hover:bg-gray-50"
              >
                Yes, delete
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
      {showFileLimitAlert && ReactDOM.createPortal(
        <div className="fixed inset-0 z-[9999] flex items-center justify-center" style={{ backgroundColor: 'rgba(128, 128, 128, 0.3)' }}>
          <div className="bg-white rounded-xl shadow-xl p-8 w-full max-w-md flex flex-col items-center relative animate-fade-in">
            <div className="flex flex-col items-center">
              <div className="rounded-full p-3 mb-3">
                <img alt="Warning" loading="lazy" width="50" height="50" decoding="async" src="assets/info-circle.svg" />
              </div>
              <span className="text-lg font-semibold text-gray-900 mb-2">File Limit Exceeded</span>
              <p className="text-gray-600 text-center mb-6">
                The maximum allowed is {maxFiles} files. <br />
                Please remove some files or select fewer files to upload.
              </p>
            </div>
            <div className="flex gap-3 w-full justify-center">
              <button
                type="button"
                onClick={() => setShowFileLimitAlert(false)}
                className="w-[162px] h-[40px] border border-[#3F73F6] cursor-pointer text-[#3F73F6] rounded-[12px] bg-white hover:bg-gray-50"
              >
                OK
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}
      {value.length > 0 && (
        <div className="space-y-2">
          {value
            .filter(file => !file.isDeleted)
            .map(file => (
              <div key={file.id}>
                {file.status !== 'completed' && !file.isDeleted && (
                  <div className="flex flex-col p-4 bg-white border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getFileIcon(getFileTypeFromMultipleSources(file), file.name || file.originalName)}
                        <p className="text-gray-900 font-medium" title={file.name}>
                          {truncateFileName(file.name || '')}
                        </p>
                      </div>
                      <button type="button" onClick={() => file.status === 'error' ? handleDirectDelete(file.id) : handleDelete(file.id)} className="text-gray-500 cursor-pointer">
                        <X className="w-5 h-5" />
                      </button>
                    </div>
                    <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                      <div
                        className={`h-full ${getProgressBarColor(file.status)} transition-all duration-300`}
                        style={{ width: `${file.progress}%` }}
                      />
                    </div>
                    <div className={`flex ${file.status === 'error' ? 'justify-start' : 'justify-end'} mt-1`}>
                      <span className={`text-sm ${getTextColor(file.status)} ${file.status === 'error' ? 'font-medium' : ''}`}>
                        {file.status === 'error' ? file.error : getStatusText(file)}
                      </span>
                    </div>
                  </div>
                )}

                {/* Completed File */}
                {(file.status === 'completed' || (file.status === 'error' && file.isEditing)) && !file.isDeleted && (
                  <div>
                    <div
                      className={`flex items-center justify-between py-[8px] px-[12px] border rounded-[12px] ${file.status === 'error' && file.isEditing
                        ? 'border-red-500'
                        : file.isEditing
                          ? 'border-[#3F73F6]'
                          : 'border-[#DCE2EB]'
                        }`}
                      style={
                        file.status === 'error' && file.isEditing
                          ? { boxShadow: '0px 0px 0px 2px rgba(239, 68, 68, 0.20)' }
                          : file.isEditing
                            ? { boxShadow: '0px 0px 0px 2px rgba(63, 115, 246, 0.20)' }
                            : {}
                      }
                    >
                      <div className="flex items-center gap-1 flex-1 min-w-0">
                        {/* File Icon */}
                        <div className="w-8 h-8 flex items-center justify-center flex-shrink-0">
                          {getFileIcon(getFileTypeFromMultipleSources(file), file.name || file.originalName)}
                        </div>

                        {/* File Name */}
                        <div className="flex flex-col flex-1 min-w-0">
                          {file.isEditing ? (
                            <div className="flex items-center gap-2">
                              <input
                                type="text"
                                value={getFileNameForEditing(file)}
                                onChange={(e) => handleFileNameUpdate(file.id, e.target.value)}
                                onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                                  if (e.key === 'Enter') {
                                    e.preventDefault();
                                    if ((e.target as HTMLInputElement).value.trim()) {
                                      toggleEditMode(file.id);
                                    }
                                  } else if (e.key === 'Escape') {
                                    toggleEditMode(file.id);
                                  }
                                }}
                                className="text-[14px] font-normal text-[#2A2E34] bg-transparent focus:outline-none min-w-0"
                                style={{
                                  width: `${inputWidths[file.id] || 200}px`,
                                  maxWidth: '100%'
                                }}
                                autoFocus
                                title={getFileNameForEditing(file)}
                              />
                              {/* Show the extension that will be appended */}
                              <span className="text-[14px] text-gray-500 flex-shrink-0">
                                .{file.originalName ?
                                  file.originalName.split('.').pop() :
                                  file.name?.split('.').pop()
                                }
                              </span>
                            </div>
                          ) : (
                            <a
                              href="#"
                              onClick={(e) => handleFileClick(e, file)}
                              className={`text-[14px] leading-[20px] text-[#3F73F6] hover:underline cursor-pointer truncate ${isGeneratingUrl[file.id] ? 'opacity-50' : ''}`}
                              title={file.name}
                            >
                              {truncateFileName(file.name?.replaceAll('/', '_') || '')}
                              {isGeneratingUrl[file.id] && (
                                <span className="ml-2 text-xs text-gray-500">Loading...</span>
                              )}
                            </a>
                          )}
                        </div>
                      </div>

                      {/* Action Icons */}
                      <div className="flex items-center gap-2 flex-shrink-0">
                        {file.isEditing ? (
                          <button
                            type="button"
                            onClick={() => {
                              if (file.name?.trim()) {
                                toggleEditMode(file.id);
                              }
                            }}
                            className="p-1 text-green-600 hover:text-green-700 transition-colors cursor-pointer"
                          >
                            <img alt="Save" loading="lazy" width="20" height="20" decoding="async" src="/assets/tick-check.svg" />
                          </button>
                        ) : (
                          <>
                            {/* Edit Icon */}
                            <button
                              type="button"
                              disabled={!canUploadFile()}
                              className="p-1 text-gray-400 hover:text-gray-600 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                              onClick={() => toggleEditMode(file.id)}
                            >
                              <img alt="Edit" loading="lazy" width="20" height="20" decoding="async" data-nimg="1" src="/assets/edit.svg" />
                            </button>

                            {/* Delete Icon */}
                            <button
                              type="button"
                              onClick={() => handleDelete(file.id)}
                              disabled={!canUploadFile()}
                              className="p-1 text-gray-400 hover:text-red-500 transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              <img alt="Delete" loading="lazy" width="20" height="20" decoding="async" data-nimg="1" src="/assets/trash-03.svg" />
                            </button>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Show error message for file extension/name validation errors */}
                    {file.status === 'error' && file.error && file.isEditing && (
                      <div className="mt-1 px-3 py-2 bg-red-50 border border-red-200 rounded-lg">
                        <p className="text-sm text-red-600 font-medium">{file.error}</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
        </div>
      )}

      {/* Upload Area */}
      <div
        className={`border rounded-[12px] px-[10px] py-[8px] text-center transition-all duration-200
            ${isDragging ? 'border-[#3F73F6] bg-[#F3F5F9]' : 'border-[#DCE2EB]'}
            ${isUploadDisabled() ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:bg-gray-50'}`}
        onDragOver={e => {
          e.preventDefault();
          if (!isUploadDisabled()) {
            setIsDragging(true);
          }
        }}
        onDragLeave={() => setIsDragging(false)}
        onDrop={handleDrop}
        onClick={() => {
          if (!isUploadDisabled()) {
            document.getElementById('file-input')?.click();
          }
        }}
      >
        <input
          id="file-input"
          type="file"
          multiple
          className="hidden"
          onChange={handleFileInput}
          accept={allowedFileTypes.join(',')}
          disabled={!canUploadFile()}
        />

        {/* Horizontal Layout */}
        <div className="flex items-center justify-center gap-2">
          {/* File Icon */}
          <div className="w-8 h-8 flex items-center justify-center rounded-lg flex-shrink-0">
            <img src="/assets/file-05.svg" alt="File Icon" className="w-5 h-5" />
          </div>

          {/* Upload Text */}
          <div className="flex-1 text-left">
            <span className="text-[14px] leading-[20px] text-[#5F6F84]">
              {getUploadMessage()}
              {(!isUploadDisabled() && value.filter(f => !f.isDeleted).length < maxFiles) && (
                <>
                  <br />
                  Supported formats: {supportedFormats}
                  <span className="mx-1">•</span>
                  Max {formatFileSize(maxFileSize)}
                </>
              )}
            </span>
          </div>

          {/* Upload Arrow Icon */}
          <div className="w-8 h-8 flex items-center justify-center flex-shrink-0">
            <img src="/assets/upload-01.svg" alt="Upload Icon" className="w-5 h-5" />
          </div>
        </div>
      </div>
    </div>
  );
});

FileUpload.displayName = 'FileUpload';

export default FileUpload;

